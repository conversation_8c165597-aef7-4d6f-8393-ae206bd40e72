'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

const ResetPassword = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState(['', '', '', '']);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showConfirmationAnimated, setShowConfirmationAnimated] = useState(false);

  useEffect(() => {
    if (showConfirmation) {
      // Start with opacity-0, then switch to opacity-100 for fade-in
      setShowConfirmationAnimated(false);
      const timer = setTimeout(() => setShowConfirmationAnimated(true), 10);
      return () => clearTimeout(timer);
    }
  }, [showConfirmation]);

  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number) => {
    const val = e.target.value;
    if (/^\d?$/.test(val)) {
      const newOtp = [...otp];
      newOtp[idx] = val;
      setOtp(newOtp);
      if (val && idx < 3) {
        const nextInput = document.getElementById(`otp-${idx + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side */}
      <div className="md:w-1/2 bg-[#F57D1C] flex flex-col justify-between p-8 text-white">
        <Image src="/icons/login-logo.svg" alt="Logo" width={150} height={50} className="w-36 mb-10" />
        <div className="flex justify-center">
          <Image src="/icons/login-illustration.svg" alt="Illustration" width={450} height={300} className="h-auto" />
        </div>
        <div className="text-center mt-3">
          <h2 className="text-[24px] font-[600] mb-3">Your Logistics Dashboard</h2>
          <p className="text-[14px] opacity-70">Everything you need easily to Order, Track, and Manage your Packages</p>
        </div>
      </div>

      {/* Right side */}
      <div className="md:w-1/2 bg-white p-10 flex flex-col justify-center">
        <div className="max-w-[572px] w-full mx-auto">
          {step === 1 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                nextStep();
              }}
            >
              <h1 className="text-[24px] font-[600] mb-3 text-black">Reset your password</h1>
              <p className="text-[14px] text-[#5D5D5D] mb-8">
                Enter the email address associated with your account and we will send you a code to reset your password.
              </p>

              <label htmlFor="email" className="text-[14px] font-[600] text-[#171717] mb-2 block">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                className="mb-6 w-full rounded-xl border border-[#E7E7E7] px-4 py-3 text-[16px] font-[600] text-gray-700 placeholder-gray-400 focus:ring-2 focus:ring-[#f47920]"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />

              <button
                type="submit"
                className="w-full bg-[#F57D1C] text-white py-3 rounded-lg text-[16px] font-[600] hover:bg-[#d96a1a]"
              >
                Continue
              </button>

              <div className="text-center mt-12">
                <a href="/sign-in" className="text-[#F57D1C] text-[14px] font-[700] hover:underline">
                  Back to Sign In
                </a>
              </div>
            </form>
          )}

          {step === 2 && (
            <>
              <h3 className="text-[24px] font-[600] mb-3 text-[#171717]">Verification</h3>
              <p className="text-[14px] font-[400] text-[#5D5D5D] mb-8">Verify your email to continue</p>

              <div className="flex justify-center mb-10">
                <div className="bg-[#FFF3ED] rounded-full p-5 w-[80px] h-[80px] flex items-center justify-center">
                  <Image src = "/icons/EnvelopeSimple.svg" alt = "" width={40} height={40}/>
                </div>
              </div>

              <p className="text-center text-[#5D5D5D] text-[16px] font-[500] mb-5">
                We’ve sent a verification code to your email
              </p>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  nextStep();
                }}
                className="flex justify-center space-x-4 mb-4"
              >
                {otp.map((digit, idx) => (
                  <input
                    key={idx}
                    id={`otp-${idx}`}
                    type="text"
                    maxLength={1}
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={digit}
                    onChange={(e) => handleOtpChange(e, idx)}
                    className="w-14 h-14 border border-[#E7E7E7] rounded-xl text-center text-[16px] font-[400] focus:outline-none focus:ring-2 focus:ring-[#f47a20]"
                    aria-label={`Verification code digit ${idx + 1}`}
                  />
                ))}
              </form>

              <p className="text-center text-[14px] font-[500] text-gray-700 mb-12">
                Didn’t receive the code?{' '}
                <button
                  type="button"
                  className="text-[#F57D1C] font-[700] hover:underline focus:outline-none"
                  onClick={() => alert('Code resent!')}
                >
                  Resend
                </button>
              </p>


              <div className="flex justify-between ">
                <button
                  type="button"
                  onClick={prevStep}
                  className="bg-[#E7E7E7] text-[#6D6D6D] rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-gray-300"
                >
                  Back
                </button>
                <button
                  onClick={nextStep}
                  className="bg-[#F57D1C] text-white rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-[#d96a1a]"
                >
                  Continue
                </button>
              </div>

              <p className="text-center text-[14px] font-[500] text-[#171717] mt-10">
                Already have an account?&nbsp;
                <a className="text-[#F57D1C] font-[600] hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </>
          )}

          {step === 3 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                setShowConfirmation(true);
              }}
              className=""
            >
              <h1 className="text-[24px] font-[600] mb-3 text-[#171717]">Reset your password</h1>
              <p className="text-[14px] font-[400] text-[#5D5D5D] mb-8">
                Enter the new password to be associated with your account
              </p>

              <div className="mb-4 relative">
                <label htmlFor="password" className="block text-[14px] font-[600] mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your new password"
                  className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 pr-12 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]"
                />
                <span className="absolute right-4 top-[69%] -translate-y-1/2 text-gray-400 cursor-pointer">
                  {/* You can add toggle password visibility here */}
                  <i className="fas fa-eye-slash"></i>
                </span>
              </div>

              <div className="mb-7 relative">
                <label htmlFor="confirm-password" className="block text-[14px] font-[600] mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  id="confirm-password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 pr-12 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]"
                />
                <span className="absolute right-4 top-[69%] -translate-y-1/2 text-gray-400 cursor-pointer">
                  {/* You can add toggle password visibility here */}
                  <i className="fas fa-eye-slash"></i>
                </span>
              </div>

              <button
                type="submit"
                className="w-full bg-[#F57D1C] text-white py-3 rounded-lg font-medium hover:bg-[#d96a1a]"
              >
                Confirm
              </button>
            </form>
          )}
        </div>
      </div>

      {showConfirmation && (
        <div
          className={`fixed inset-0 z-50 bg-white flex items-center justify-center transition-opacity duration-700 ease-in-out ${
            showConfirmationAnimated ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <main className="flex flex-col items-center space-y-6">
            <Image src="/icons/login-check.svg" alt="check" width={100} height={50} className="w-24 mb-8" />
            <h1 className="text-center text-[#171717] text-[24px] font-[600] leading-[140%] mb-3">
              Great Job 🎉<br />
              <span className="block">Your password was successfully changed</span>
            </h1>
            <p className="text-center text-[#5D5D5D] text-[14px] leading-tight max-w-xs">
              Use the new password to log in to your account
            </p>
            <a
              href="/sign-in"
              className="mt-4 bg-[#F57D1C] hover:bg-[#d96a1a] w-[122px] text-white text-sm font-medium text-center rounded-lg px-6 py-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1"
            >
              Continue
            </a>
          </main>
        </div>
      )}
    </div>
  );
};

export default ResetPassword;
