
import Image from 'next/image'
import React from 'react'

const SignIn = () => {
  return (
	<div className="min-h-screen flex flex-col md:flex-row">
   <div className="md:w-1/2 bg-[#F57D1C] flex flex-col justify-between p-8">
    <div>
     <Image alt="KOOL LOGISTICS white logo with arrow" className="w-36 mb-10" height="50" src="/icons/login-logo.svg" width="150"/>
    </div>
    <div className="flex justify-center">
     <Image alt="Illustration of a man" className="max-w-full h-auto" height="300" src="/icons/login-illustration.svg" width="450"/>
    </div>
    <div className="text-center text-white mt-3">
     <h2 className="text-[24px] font-[600] mb-3">
      Your Logistics Dashboard
     </h2>
     <p className="text-[14px] text-[#F6F6F6] opacity-70">
      Everything you need easily to Order, Track, and Manage your Packages
     </p>
    </div>
   </div>
   <div className="md:w-1/2 bg-white p-10 flex flex-col justify-center">
    <div className="max-w-[572px] w-full mx-auto">
     <h1 className="text-[24px] font-[600] mb-3">
      Sign in to your account
     </h1>
     <p className="text-sm text-[#5D5D5D] mb-8 ">
      Welcome back! please enter your detail
     </p>
     <form>
      <div className="mb-6">
       <label className="block text-sm font-semibold mb-2">
        Email Address
       </label>
       <input className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]" id="email" placeholder="Enter your email address" type="email"/>
      </div>
      <div className="mb-6 relative">
       <label className="block text-sm font-semibold mb-2">
        Password
       </label>
       <input className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 pr-12 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]" id="password" placeholder="Enter your password" type="password"/>
       <span className="absolute right-4 top-[69%] -translate-y-1/2 text-gray-400 cursor-pointer">
        <i className="fas fa-eye-slash">
        </i>
       </span>
      </div>
      <div className="flex justify-between items-center mb-8 text-sm">
        <label className="flex items-center space-x-2 text-gray-700">
  {/* Hidden real checkbox */}
  <input
    type="checkbox"
    defaultChecked
    id="remember"
    className="sr-only peer"
  />

  {/* Custom styled checkbox */}
  <label
    htmlFor="remember"
    className="w-4 h-4 rounded-[4px] border border-gray-300 peer-checked:bg-[#F57D1C] peer-checked:border-[#F57D1C] relative flex items-center justify-center cursor-pointer transition-all duration-150"
  >
    <i className="ph-bold ph-check text-white text-xs"></i>
  </label>

  {/* Label text */}
  <span className="text-[14px] font-[500] cursor-pointer">
    Remember me
  </span>
</label>

        <a className="text-[#f47a20] font-semibold hover:underline" href="/forgot-password">
          Forgot Password?
        </a>
      </div>
       <button className="w-full bg-[#f47a20] text-white py-3 rounded-lg font-semibold hover:bg-[#d96a1a] transition-colors" type="submit">
          Sign In
        </button>

        <p className="text-center text-[14px] font-[400] mt-8 text-[#171717]">
          Dont have an account?{" "}
          <a className="text-[#F57D1C] font-[500] hover:underline" href="/sign-up">
            Sign Up
          </a>
        </p>
     </form>
    </div>
   </div>
  </div>
  )
}

export default SignIn