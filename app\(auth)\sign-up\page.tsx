'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Check } from "@phosphor-icons/react";

const SignUp = () => {
  // Step 1: Account type + personal info
  // Step 2: Business info
  // Step 3: OTP verification

  const [step, setStep] = useState(1);

  // Form state, you can expand as needed
  const [accountType, setAccountType] = useState('individual');
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [termsChecked, setTermsChecked] = useState(false);

  const [companyName, setCompanyName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [companySize, setCompanySize] = useState('');

  const [otp, setOtp] = useState(['', '', '', '']); // array for 4 digits

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showConfirmationAnimated, setShowConfirmationAnimated] = useState(false);

  // Handlers to go next/prev
  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const handleOtpChange = (e, idx) => {
    const val = e.target.value;
    if (/^\d?$/.test(val)) { // allow only single digits or empty
      const newOtp = [...otp];
      newOtp[idx] = val;
      setOtp(newOtp);
      // Auto-focus next input if digit entered
      if (val && idx < 3) {
        const nextInput = document.getElementById(`otp-${idx + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Step indicator dots
  const StepIndicator = () => (
    <div className="flex space-x-2 mb-8 mt-4 justify-center">
      {[1, 2, 3].map((num) => (
        <span
          key={num}
          className={`w-3 h-3 rounded-full block ${
            step === num ? 'bg-[#F57D1C]' : 'bg-[#FBBF76] opacity-20'
          }`}
          aria-label={`Step ${num}`}
        />
      ))}
    </div>
  );

  const [selected, setSelected] = useState('individual');

  const options = [
    { label: 'Individual', value: 'individual' },
    { label: 'Admin', value: 'admin' },
  ];

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-white">
      {/* Left panel */}
      <div className="md:w-1/2 bg-[#F57D1C] flex flex-col justify-between p-8 text-white">
        <div>
          <Image
            alt="KOOL LOGISTICS white logo"
            className="w-36 mb-10"
            height={50}
            src="/icons/login-logo.svg"
            width={150}
          />
        </div>
        <div className="flex justify-center">
          <Image
            alt="Illustration of a man"
            className="max-w-full h-auto"
            height={300}
            src="/icons/login-illustration.svg"
            width={450}
          />
        </div>
        <div className="text-center mt-3">
          <h2 className="text-[24px] font-[600] mb-3">Your Logistics Dashboard</h2>
          <p className="text-[14px] opacity-70">
            Everything you need easily to Order, Track, and Manage your Packages
          </p>
        </div>
      </div>

      {/* Right panel: forms */}
      <div className="md:w-1/2 p-10 flex flex-col justify-center">
        <div className="max-w-[572px] w-full mx-auto">
          {step === 1 && (
            <>
              <h1 className="text-[24px] font-[600] mb-3">Create your account</h1>
              <p className="text-[14px] font-[400] text-[#5D5D5D] mb-8">Enter your details to get started</p>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  nextStep();
                }}
              >
                <fieldset className="flex items-center space-x-6 mb-8 justify-center">
                  {options.map((option) => (
                    <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name="accountType"
                        value={option.value}
                        checked={accountType === option.value}
                        onChange={() => setAccountType(option.value as 'individual' | 'admin')}
                        className="sr-only"
                      />
                      <span
                        className={`w-4 h-4 flex items-center justify-center rounded-full border 
                          ${accountType === option.value
                            ? 'bg-[#F57D1C] border-[#F57D1C] text-white'
                            : 'border-gray-300 text-transparent'}
                        `}
                      >
                        <i className="ph-bold ph-check text-[11px] font-bold"></i>
                      </span>
                      <span className="text-[14px] font-[500] text-[#344054]">
                        {option.label}
                      </span>
                    </label>
                  ))}
                </fieldset>

                <div className="mb-4">
                  <label htmlFor="fullName" className="block text-[14px] font-[600] mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-[14px] font-[600] mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]"
                  />
                </div>

                <div className="mb-4 relative">
                  <label htmlFor="password" className="block text-[14px] font-[600] mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 pr-12 text-[#171717] text-[16px] font-semibold placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f37720]"
                  />
                  <span className="absolute right-4 top-[69%] -translate-y-1/2 text-gray-400 cursor-pointer">
                    {/* You can add toggle password visibility here */}
                    <i className="fas fa-eye-slash"></i>
                  </span>
                </div>

                <div className="flex items-start mb-8">
                  {/* Visually hidden actual checkbox for accessibility */}
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsChecked}
                    onChange={() => setTermsChecked(prev => !prev)}
                    className="sr-only peer"
                  />

                  {/* Custom checkbox */}
                  <label
                    htmlFor="terms"
                    className="w-4 h-4 mt-1 rounded-[4px] border border-gray-300 peer-checked:bg-[#F57D1C] peer-checked:border-[#F57D1C] relative flex items-center justify-center cursor-pointer transition-all duration-150"
                  >
                    <Check weight="bold" className="text-white text-sm" />
                  </label>

                  {/* Label text */}
                  <label
                    htmlFor="terms"
                    className="ml-2 text-xs text-[#64748B] leading-5 cursor-pointer"
                  >
                    By creating an account means you agree to the
                    <strong className="text-black">&nbsp;Terms <br />&amp; Conditions&nbsp;</strong> and our
                    <strong className="text-black">&nbsp;Privacy Policy</strong>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <StepIndicator />
                  <button
                    type="submit"
                    className="bg-[#F57D1C] text-white rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-[#d96a1a]"
                  >
                    Next
                  </button>
                </div>
              </form>
              <p className="text-center text-[14px] font-[500] text-[#171717] mt-10">
                Already have an account?&nbsp;
                <a className="text-[#F57D1C] font-[600] hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </>
          )}

          {step === 2 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                nextStep();
              }}
            >
              <h3 className="text-[24px] font-[600] mb-3 text-[#171717]">Company Information</h3>
              <p className="text-[14px] font-[400] text-[#5D5D5D] mb-8">Tell us about your business</p>

              <label htmlFor="companyName" className="block text-[14px] font-[600] text-[#171717] mb-2">
                Company Name
              </label>
              <input
                type="text"
                id="companyName"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Enter Company Name"
                className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 mb-6 text-[16px] font-[600] text-[#171717] placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f47a20]"
              />

              <label htmlFor="businessType" className="block text-[14px] font-[600] text-[#171717] mb-2">
                Business Type
              </label>
              <select
                id="businessType"
                value={businessType}
                onChange={(e) => setBusinessType(e.target.value)}
                className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 mb-6 text-[16px] font-[600] text-[#171717] placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f47a20] appearance-none"
              >
                <option value="">Select your business type</option>
                <option value="type1">Type 1</option>
                <option value="type2">Type 2</option>
                <option value="type3">Type 3</option>
              </select>

              <label htmlFor="companySize" className="block text-[14px] font-[600] text-[#171717] mb-2">
                Company Size
              </label>
              <select
                id="companySize"
                value={companySize}
                onChange={(e) => setCompanySize(e.target.value)}
                className="w-full border border-[#E7E7E7] rounded-xl px-4 py-3 mb-10 text-[16px] font-[600] text-[#171717] placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#f47a20] appearance-none"
              >
                <option value="">Select company size</option>
                <option value="1-10">0 - 10 persons</option>
                <option value="11-50">11 - 50 persons</option>
                <option value="51-200">51 - 100 persons</option>
                <option value="300-1000">300 - 1000 persons</option>
              </select>


              <div className="flex justify-between">
              <StepIndicator />
              <div className="flex justify-between space-x-4">
                <button
                  type="button"
                  onClick={prevStep}
                  className="bg-[#E7E7E7] text-[#6D6D6D] rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-gray-300 transition"
                >
                  Previous
                </button>
                <button
                  type="submit"
                  className="bg-[#F57D1C] text-white rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-[#d96a1a] transition"
                >
                  Next
                </button>
              </div>
              </div>

              <p className="text-center text-[14px] font-[500] text-[#171717] mt-10">
                Already have an account?&nbsp;
                <a className="text-[#F57D1C] font-[600] hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </form>
          )}

          {step === 3 && (
            <>
              <h3 className="text-[24px] font-[600] mb-3 text-[#171717]">Verification</h3>
              <p className="text-[14px] font-[400] text-[#5D5D5D] mb-8">Verify your email to continue</p>

              <div className="flex justify-center mb-10">
                <div className="bg-[#FFF3ED] rounded-full p-5 w-[80px] h-[80px] flex items-center justify-center">
                  <Image src = "/icons/EnvelopeSimple.svg" alt = "" width={40} height={40}/>
                </div>
              </div>

              <p className="text-center text-[#5D5D5D] text-[16px] font-[500] mb-5">
                We’ve sent a verification code to your email
              </p>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (otp.some((digit) => digit === '')) {
                    alert('Please enter the full OTP code');
                    return;
                  }
                  alert('Verification complete!');
                  // Add further logic here
                }}
                className="flex justify-center space-x-4 mb-4"
              >
                {otp.map((digit, idx) => (
                  <input
                    key={idx}
                    id={`otp-${idx}`}
                    type="text"
                    maxLength="1"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={digit}
                    onChange={(e) => handleOtpChange(e, idx)}
                    className="w-14 h-14 border border-[#E7E7E7] rounded-xl text-center text-[16px] font-[400] focus:outline-none focus:ring-2 focus:ring-[#f47a20]"
                    aria-label={`Verification code digit ${idx + 1}`}
                  />
                ))}
              </form>

              <p className="text-center text-[14px] font-[500] text-gray-700 mb-12">
                Didn’t receive the code?{' '}
                <button
                  type="button"
                  className="text-[#F57D1C] font-[700] hover:underline focus:outline-none"
                  onClick={() => alert('Code resent!')}
                >
                  Resend
                </button>
              </p>


              <div className="flex justify-between">
                <StepIndicator />
                <div className="flex justify-between space-x-4">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-[#E7E7E7] text-[#6D6D6D] rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-gray-300"
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    className="bg-[#F57D1C] text-white rounded-xl px-6 py-4 w-[122px] font-semibold hover:bg-[#d96a1a]"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowConfirmation(true);
                      setTimeout(() => setShowConfirmationAnimated(true), 50); // triggers fade-in
                    }}
                  >
                    Complete
                  </button>
                </div>
              </div>

              <p className="text-center text-[14px] font-[500] text-[#171717] mt-10">
                Already have an account?&nbsp;
                <a className="text-[#F57D1C] font-[600] hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </>
          )}
        </div>
      </div>

      {showConfirmation && (
        <div
          className={`fixed inset-0 z-50 bg-white flex items-center justify-center transition-opacity duration-700 ease-in-out ${
            showConfirmationAnimated ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <main className="flex flex-col items-center space-y-6">
            <Image src="/icons/login-check.svg" alt="check" width={100} height={50} className="w-24 mb-8" />
            <h1 className="text-center text-[#171717] text-[24px] font-[600] leading-[140%] mb-3">
              Great Job 🎉<br />
              <span className="block">Your email is verified</span>
            </h1>
            <p className="text-center text-[#5D5D5D] text-[14px] leading-tight max-w-xs">
              Use it to log in to your account
            </p>
            <a
              href="/sign-in"
              className="mt-4 bg-[#F57D1C] hover:bg-[#d96a1a] w-[122px] text-white text-sm font-medium text-center rounded-lg px-6 py-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1"
            >
              Continue
            </a>
          </main>
        </div>
      )}
    </div>
  );
};

export default SignUp;
