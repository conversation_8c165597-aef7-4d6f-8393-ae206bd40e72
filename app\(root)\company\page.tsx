'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import RequestQuoteButton from '@/components/ui/RequestQuoteButton'

const Company = () => {
  const [activeTab, setActiveTab] = useState('mission')
  return (
    <div className="min-h-screen pt-14">
      {/* Hero Section with Globe */}
      <section className="py-8 md:py-12 lg:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-6 md:space-y-8">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
                <p className="text-[#454545] font-medium text-xs md:text-sm tracking-wider uppercase">WHO WE ARE</p>
              </div>
              <h1 className="font-manrope font-semibold text-[32px] md:text-[48px] lg:text-[56px] leading-[120%] tracking-[-1%] text-woodsmoke-950">
                Connecting the World Through Smart Logistics
              </h1>
              <p className="text-base md:text-lg text-woodsmoke-600 leading-relaxed">
                At Kool Logistics, we&apos;re revolutionizing the way goods move across the globe. Our innovative solutions combine cutting-edge technology with reliable service to deliver excellence every time.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/services" className="bg-primary-500 hover:bg-primary-400 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center">
                  Explore Our Services
                </Link>
                <Link href="/contact" className="border border-primary-500 text-primary-500 hover:bg-primary-50 px-6 py-3 rounded-lg font-medium transition-colors text-center">
                  Get in Touch
                </Link>
              </div>
            </div>

            {/* Right Content - Globe Image */}
            <div className="relative order-1 lg:order-2">
              <div className="relative w-full h-[300px] md:h-[400px] lg:h-[500px] flex items-center justify-center">
                <Image
                  src="/images/globe.svg"
                  alt="Global Logistics Network"
                  width={500}
                  height={500}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-8 md:py- bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-12 items-start mb-8 md:mb-12">
            <div className="space-y-4 md:space-y-6">
              <div className="flex items-center gap-2">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
                <p className="text-[#454545] font-medium text-xs md:text-sm tracking-wider uppercase">WHO WE ARE</p>
              </div>
              <h1 className="font-manrope font-semibold text-[24px] md:text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Our Innovative Solutions for Your Delivery Services.
              </h1>
            </div>
            <div className="space-y-4 md:space-y-6">
              <p className="text-sm md:text-lg text-woodsmoke-600 leading-relaxed">
                Koolboks Logistics offers a range of services designed to meet diverse needs. Whether for homes, businesses, our services ensure reliable cooling without reliance on traditional energy sources.
              </p>
              <Link href="/services" className="bg-primary-500 hover:bg-primary-400 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors inline-block text-sm md:text-base">
                Our Services
              </Link>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative w-full h-[200px] md:h-[300px] lg:h-[400px] rounded-2xl overflow-hidden">
            <Image
              src="/images/hero-company.svg"
              alt="Company Hero"
              width={1200}
              height={400}
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 md:py-8 lg:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div className="text-center">
              <div className="font-manrope font-semibold text-[24px] md:text-[40px] lg:text-[56px] leading-[120%] text-primary-500 mb-2">1M+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-sm lg:text-base">Shipments Delivered</p>
            </div>
            <div className="text-center">
              <div className="font-manrope font-semibold text-[24px] md:text-[40px] lg:text-[56px] leading-[120%] text-primary-500 mb-2">50+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-sm lg:text-base">Cities Covered</p>
            </div>
            <div className="text-center">
              <div className="font-manrope font-semibold text-[24px] md:text-[40px] lg:text-[56px] leading-[120%] text-primary-500 mb-2">99%</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-sm lg:text-base">On-Time Delivery</p>
            </div>
            <div className="text-center">
              <div className="font-manrope font-semibold text-[24px] md:text-[40px] lg:text-[56px] leading-[120%] text-primary-500 mb-2">24/7</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-sm lg:text-base">Customer Support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="pt-6 pb-8 md:py-10 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:grid lg:grid-cols-2 gap-8 md:gap-16 items-center">
          <div className="relative order-2 lg:order-1 hidden lg:block">
            <div className="w-full h-[416px] rounded-2xl overflow-hidden">
              <Image
                src="/images/team.svg"
                alt="Our Team"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="h-auto lg:h-[416px] flex flex-col justify-between lg:order-2">
            <div className="space-y-4 md:space-y-4">
              <h2 className="font-manrope font-semibold text-[24px] md:text-[32px] leading-[120%] tracking-[0%] text-woodsmoke-950  mt-8 mb-6">
                The values that drive everything we do.
              </h2>

              {/* Tab Navigation */}
              <div className="flex gap-4 mb-6 lg:mb-8">
                <button
                  onClick={() => setActiveTab('mission')}
                  className={`px-4 py-2 rounded-lg font-medium text-sm md:text-base transition-colors ${
                    activeTab === 'mission'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 text-woodsmoke-600 hover:bg-gray-200'
                  }`}
                >
                  Our Mission
                </button>
                <button
                  onClick={() => setActiveTab('values')}
                  className={`px-4 py-2 rounded-lg font-medium text-sm md:text-base transition-colors ${
                    activeTab === 'values'
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 text-woodsmoke-600 hover:bg-gray-200'
                  }`}
                >
                  Our Values
                </button>
              </div>
            </div>

            {/* Mission Content */}
            {activeTab === 'mission' && (
              <div className="space-y-4 lg:flex-1 lg:flex lg:flex-col lg:justify-center">
                <h3 className="text-[20px] md:text-xl font-bold text-woodsmoke-950 leading-[130%]">
                  Redefining Logistics for a Faster, Smarter World
                </h3>
                <p className="text-[16px] md:text-base text-woodsmoke-600 leading-[160%]">
                 For athletes, high altitude produces two contradictory effects on performance. For explosive events Physiological respiration involves the mechanisms that ensure that the composition of the functional. The long barrow was built on land previously inhabited in the Mesolithic period. It consisted of a Physical space is often conceived in three linear dimensions, although modern physicists
                </p>
              </div>
            )}

            {/* Values Content */}
            {activeTab === 'values' && (
              <div className="grid grid-cols-2 gap-4 lg:flex-1 lg:content-center">
                {/* Reliability */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Headset.svg"
                      alt="Reliability Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[18px] font-semibold text-woodsmoke-950 leading-[130%]">Reliability</h3>
                  <p className="text-[14px] text-woodsmoke-600 leading-[150%]">
                    Ensuring every shipment reaches its destination safely and on time.
                  </p>
                </div>

                {/* Innovation */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Lightbulb.svg"
                      alt="Innovation Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[18px] font-semibold text-woodsmoke-950 leading-[130%]">Innovation</h3>
                  <p className="text-[14px] text-woodsmoke-600 leading-[150%]">
                    Leveraging cutting-edge technology for smarter logistics solutions.
                  </p>
                </div>

                {/* Sustainability */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Leaf.svg"
                      alt="Sustainability Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[18px] font-semibold text-woodsmoke-950 leading-[130%]">Sustainability</h3>
                  <p className="text-[14px] text-woodsmoke-600 leading-[150%]">
                    Committed to eco-friendly practices and reducing our carbon footprint.
                  </p>
                </div>

                {/* Customer Centric Approach */}
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center">
                    <Image
                      src="/icons/Clock.svg"
                      alt="Customer Centric Icon"
                      width={24}
                      height={24}
                      className="w-6 h-6 text-[#FF6B35]"
                    />
                  </div>
                  <h3 className="text-[18px] font-semibold text-woodsmoke-950 leading-[130%]">Customer Centric Approach</h3>
                  <p className="text-[14px] text-woodsmoke-600 leading-[150%]">
                    Prioritizing client needs with tailored logistics strategies.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-6 md:py-6 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="w-full max-w-[980px] h-auto md:h-[106px] mx-auto grid grid-cols-2 md:flex md:justify-between items-center opacity-100 gap-4 md:gap-0">
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">1M+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Shipments Delivered</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">50+</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Cities Covered</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">99%</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">On-Time Delivery</p>
            </div>
            <div className="flex flex-col items-center justify-center text-center">
              <div className="font-manrope font-semibold text-[32px] md:text-[56px] leading-[120%] tracking-[-1%] text-center opacity-100 text-primary-500">24/7</div>
              <p className="text-woodsmoke-600 font-medium text-xs md:text-base">Customer Support</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA and Delivery Section */}
      <section className="py-8 md:py-16 lg:py-20 px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:grid lg:grid-cols-12 gap-0 min-h-[300px] md:min-h-[400px]">
            {/* CTA Content Container - Full Width on Mobile */}
            <div className="lg:col-span-7 bg-[#FEEED6] rounded-2xl overflow-hidden h-full order-1 lg:order-2">
              <div className="h-[300px] md:h-[400px] flex flex-col justify-center px-6 md:px-8 lg:px-12 xl:px-16">
                <div className="space-y-3 md:space-y-4 lg:space-y-6">
                  <p className="text-[#666666] font-medium text-xs md:text-xs lg:text-sm tracking-[0.1em] uppercase">HIRE US FOR KOOL DELIVERY</p>
                  <h2 className="font-manrope font-semibold text-[24px] md:text-[36px] lg:text-[48px] leading-[120%] tracking-[0%] text-[#2D2D2D]">
                    Looking for the best<br />logistics transport<br />service?
                  </h2>
                  <div className="pt-3 md:pt-4 lg:pt-6">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 md:gap-4 lg:gap-6">
                      <RequestQuoteButton />
                      <div className="flex items-center gap-2 lg:gap-3 text-[#666666]">
                        <Image
                          src="/icons/PhoneCall.svg"
                          alt="Phone"
                          width={18}
                          height={18}
                          className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5"
                        />
                        <span className="font-medium text-xs md:text-sm lg:text-base">081 342 167111</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Image Container - Desktop Only */}
            <div className="hidden lg:block lg:col-span-5 bg-white rounded-2xl overflow-hidden h-full order-2 lg:order-1">
              <div className="w-full h-[400px] flex items-center justify-center">
                <Image
                  src="/images/delivery.png"
                  alt="Delivery Service"
                  width={400}
                  height={200}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
  )
}

export default Company
