'use client'

import React from 'react'
import Image from 'next/image'
import FAQ from '@/components/FAQ'
import Map from '@/components/Map'
import ContactForm from '@/components/forms/ContactForm'

const Contact = () => {
  return (
    <div className="min-h-screen bg-white">
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      {/* Contact Header */}
      <section className="py-6 md:py-8 lg:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-4 md:mb-6">
            <Image
              src="/icons/cubeicon.svg"
              alt="Cube Icon"
              width={20}
              height={20}
              className="w-4 h-4 md:w-5 md:h-5"
            />
            <p className="text-[#454545] font-medium text-xs md:text-sm tracking-wider uppercase">CONTACT US</p>
          </div>
          <h1 className="font-manrope font-semibold text-[24px] md:text-[32px] lg:text-[40px] leading-[120%] text-center text-[#2D2D2D]">
            We&apos;re Here to Help –<br className="hidden md:block" />
            <span className="md:hidden"> </span>Get in Touch Today!
          </h1>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-8 md:py-12 lg:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16">
            {/* Left Column - Contact Info */}
            <div className="order-1 lg:order-1">
              <h2 className="text-[24px] md:text-[28px] lg:text-[32px] font-bold text-[#2D2D2D] mb-3 md:mb-4">Get in touch</h2>
              <p className="text-[#666666] text-[14px] md:text-[16px] leading-[1.6] mb-8 md:mb-12 lg:mb-16">
                We&apos;d love to hear from you. Our friendly team is<br className="hidden md:block" />
                <span className="md:hidden"> </span>always here to chat.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-x-8 lg:gap-x-16 md:gap-y-8 lg:gap-y-12">
                {/* Chat with Us */}
                <div>
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-[#FFF4F0] rounded-[8px] md:rounded-[12px] flex items-center justify-center mb-3 md:mb-4">
                    <Image
                      src="/icons/ChatCircleDots.svg"
                      alt="Chat Icon"
                      width={20}
                      height={20}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[14px] md:text-[16px] mb-1 md:mb-2">Chat with Us</p>
                  <p className="text-[#2D2D2D] text-[16px] md:text-[18px] font-semibold">
                    (+234) 803 123 456
                  </p>
                </div>

                {/* Visit Us */}
                <div>
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-[#FFF4F0] rounded-[8px] md:rounded-[12px] flex items-center justify-center mb-3 md:mb-4">
                    <Image
                      src="/icons/MapPin.svg"
                      alt="Location Icon"
                      width={20}
                      height={20}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[14px] md:text-[16px] mb-1 md:mb-2">Visit Us</p>
                  <p className="text-[#2D2D2D] text-[16px] md:text-[18px] font-semibold">
                    46 Owode-Abeokuta Road,<br />
                    Owode, Ogun
                  </p>
                </div>

                {/* Send an Email */}
                <div>
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-[#FFF4F0] rounded-[8px] md:rounded-[12px] flex items-center justify-center mb-3 md:mb-4">
                    <Image
                      src="/icons/EnvelopeSimple.svg"
                      alt="Email Icon"
                      width={20}
                      height={20}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[14px] md:text-[16px] mb-1 md:mb-2">Send an Email</p>
                  <p className="text-[#2D2D2D] text-[16px] md:text-[18px] font-semibold">
                    <EMAIL>
                  </p>
                </div>

                {/* Social Media */}
                <div>
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-[#FFF4F0] rounded-[8px] md:rounded-[12px] flex items-center justify-center mb-3 md:mb-4">
                    <Image
                      src="/icons/WhatsappLogo.svg"
                      alt="Social Icon"
                      width={20}
                      height={20}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </div>
                  <p className="text-[#666666] text-[14px] md:text-[16px] mb-3 md:mb-4">Social Media</p>
                  <div className="flex gap-2 md:gap-3">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-[#2D2D2D] rounded-md md:rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/x.svg"
                        alt="X (Twitter)"
                        width={16}
                        height={16}
                        className="w-3 h-3 md:w-4 md:h-4"
                      />
                    </div>
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-[#2D2D2D] rounded-md md:rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Linkedin.svg"
                        alt="LinkedIn"
                        width={16}
                        height={16}
                        className="w-3 h-3 md:w-4 md:h-4"
                      />
                    </div>
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-[#2D2D2D] rounded-md md:rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Instagram.svg"
                        alt="Instagram"
                        width={16}
                        height={16}
                        className="w-3 h-3 md:w-4 md:h-4"
                      />
                    </div>
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-[#2D2D2D] rounded-md md:rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Facebook.svg"
                        alt="Facebook"
                        width={16}
                        height={16}
                        className="w-3 h-3 md:w-4 md:h-4"
                      />
                    </div>
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-[#2D2D2D] rounded-md md:rounded-lg flex items-center justify-center">
                      <Image
                        src="/icons/Youtube.svg"
                        alt="YouTube"
                        width={16}
                        height={16}
                        className="w-3 h-3 md:w-4 md:h-4"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Contact Form */}
            <ContactForm />
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-4 md:py-6 lg:py-8 bg-white">
        <div className="w-full px-4 md:px-6 lg:px-0">
          <div className="max-w-7xl mx-auto">
            <div className="w-full h-[250px] md:h-[350px] lg:h-[400px] overflow-hidden">
              <Map />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default Contact