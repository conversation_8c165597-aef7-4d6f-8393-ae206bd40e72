"use client"
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import FAQ from '@/components/FAQ'
import RequestQuoteButton from '@/components/ui/RequestQuoteButton'

const Services = () => {

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-8 bg-white px-6">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div>
              <div className="flex items-center gap-2 mb-6">
                <Image
                  src="/icons/cubeicon.svg"
                  alt="Cube Icon"
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
                <p className="text-[#454545] font-medium text-sm tracking-wider uppercase">VALUE OFFER</p>
              </div>
              <h1 className="font-manrope font-semibold text-[24px] md:text-[40px] leading-[120%] tracking-[0%] text-woodsmoke-950">
                Delivering your Packages reliably
              </h1>
            </div>

            {/* Right Content */}
            <div>
              <p className="text-[#666666] text-[16px] leading-[1.6] mb-8">
                Unlock the full potential of your business with our comprehensive logistics solutions. We optimize supply chains, enhance operations, and drive success worldwide.
              </p>
              <RequestQuoteButton />
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-white px-6">
        <div className="max-w-[1200px] mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* E-commerce */}
            <Link href="/services/service-details-1" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">E-commerce</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>

            {/* Fast Moving Consumer Goods */}
            <Link href="/services/service-details-2" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">Fast Moving Consumer Goods</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>

            {/* Fast Moving Consumer Goods */}
            <Link href="/services/service-details-3" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">Fast Moving Consumer Goods</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>

            {/* Medical Goods */}
            <Link href="/services/service-details-4" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">Medical Goods</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>

            {/* Medical Goods */}
            <Link href="/services/service-details-5" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">Medical Goods</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>

            {/* Automotive */}
            <Link href="/services/service-details-6" className="block">
              <div className="bg-white p-8 rounded-[16px] border border-[#E5E5E5] cursor-pointer hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-[#FFF4F0] rounded-[12px] flex items-center justify-center mb-6">
                  <Image
                    src="/icons/Headset.svg"
                    alt="Headset Icon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h3 className="text-[20px] font-semibold text-[#2D2D2D] mb-4">Automotive</h3>
                <p className="text-[#666666] text-[14px] leading-[1.6]">
                  Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.
                </p>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ className="bg-white" />

      {/* Footer */}
     
    </div>
  )
}

export default Services