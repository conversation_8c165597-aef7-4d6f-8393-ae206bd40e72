import ROUTES from "@/constants/routes";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import NavLinks from "./NavLinks";

const Navbar = () => {
return (
      <nav className="fixed z-50 w-full bg-white/80 backdrop-blur-sm rounded-b-[12px]">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <Link href={ROUTES.HOME} className="">
          <Image
            src="/images/site-logo.png"
            width={118}
            height={56}
            alt="Kool logistics logo"
          />
        </Link>
        <NavLinks />
        <div className="">
          <Link href={ROUTES.SIGN_IN}>
            <button className="btn-secondary-no-bg">Login</button>
          </Link>
          <Link href={ROUTES.SIGN_UP}>
            <button className="btn-primary">Sign Up</button>
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;